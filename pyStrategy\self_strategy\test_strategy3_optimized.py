# -*- coding: utf-8 -*-
"""
Strategy3 优化版本测试脚本
验证优化后的策略功能是否正常
"""

import sys
import os
import time
import numpy as np
from typing import Dict, Any

# 添加路径以导入策略
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from Strategy3_optimized import (
        Strategy3Optimized,
        SignalOutput,
        UnifiedFuzzyProcessor,
        StructuralAnalysisProcessor,
        StatisticalAnalysisProcessor,
        EnhancedSignalProcessor,
        SignalFusionCenter
    )
    print("✓ 成功导入优化版本策略模块")
except ImportError as e:
    print(f"✗ 导入策略模块失败: {e}")
    print("这可能是因为缺少pythongo模块，但不影响核心功能测试")
    # 继续测试，不退出

class TestKLineData:
    """测试用的K线数据类"""
    def __init__(self, close: float, high: float = None, low: float = None, 
                 open_price: float = None, volume: int = 1000):
        self.close = close
        self.high = high or close * 1.01
        self.low = low or close * 0.99
        self.open = open_price or close
        self.volume = volume
        self.datetime = time.time()

def test_signal_output():
    """测试统一信号输出格式"""
    print("\n=== 测试统一信号输出格式 ===")
    
    # 测试正常信号
    signal = SignalOutput(0.5, 0.8, {"test": "data"})
    assert -1.0 <= signal.signal <= 1.0, "信号值应在[-1,1]范围内"
    assert 0.0 <= signal.confidence <= 1.0, "置信度应在[0,1]范围内"
    
    # 测试边界值
    signal_extreme = SignalOutput(2.0, -0.5)  # 应被限制
    assert signal_extreme.signal == 1.0, "信号值应被限制在1.0"
    assert signal_extreme.confidence == 0.0, "置信度应被限制在0.0"
    
    print("✓ 统一信号输出格式测试通过")

def test_fuzzy_processor():
    """测试统一模糊推理处理器"""
    print("\n=== 测试统一模糊推理处理器 ===")
    
    processor = UnifiedFuzzyProcessor()
    
    # 测试不同市场条件
    test_cases = [
        {"trend": 0.3, "volatility": 0.02},   # 上升趋势，低波动
        {"trend": -0.4, "volatility": 0.03},  # 下降趋势，中等波动
        {"trend": 0.0, "volatility": 0.08},   # 无趋势，高波动
    ]
    
    for i, market_data in enumerate(test_cases):
        result = processor.process(market_data)
        assert isinstance(result, SignalOutput), f"测试案例{i+1}: 应返回SignalOutput对象"
        assert -1.0 <= result.signal <= 1.0, f"测试案例{i+1}: 信号值超出范围"
        assert 0.0 <= result.confidence <= 1.0, f"测试案例{i+1}: 置信度超出范围"
        print(f"  案例{i+1}: 信号={result.signal:.3f}, 置信度={result.confidence:.3f}")
    
    print("✓ 统一模糊推理处理器测试通过")

def test_structural_analysis():
    """测试结构性分析处理器"""
    print("\n=== 测试结构性分析处理器 ===")
    
    processor = StructuralAnalysisProcessor()
    
    # 模拟价格序列
    for i in range(15):
        price = 100 + i * 0.5 + np.sin(i * 0.5) * 2  # 带趋势和周期性的价格
        market_data = {"price": price}
        result = processor.process(market_data)
        
        if i >= 10:  # 有足够数据后开始测试
            assert isinstance(result, SignalOutput), "应返回SignalOutput对象"
            assert -1.0 <= result.signal <= 1.0, "信号值超出范围"
            print(f"  步骤{i+1}: 价格={price:.2f}, 信号={result.signal:.3f}")
    
    print("✓ 结构性分析处理器测试通过")

def test_statistical_analysis():
    """测试统计分析处理器"""
    print("\n=== 测试统计分析处理器 ===")
    
    processor = StatisticalAnalysisProcessor()
    
    # 模拟不同波动率的价格序列
    volatilities = [0.01, 0.03, 0.06]  # 低、中、高波动率
    
    for vol in volatilities:
        for i in range(12):
            price = 100 + np.random.normal(0, vol * 100)
            market_data = {"price": price, "volatility": vol}
            result = processor.process(market_data)
            
            if i >= 10:
                assert isinstance(result, SignalOutput), "应返回SignalOutput对象"
                print(f"  波动率{vol:.2f}, 步骤{i+1}: 信号={result.signal:.3f}")
    
    print("✓ 统计分析处理器测试通过")

def test_enhanced_processor():
    """测试增强信号处理器"""
    print("\n=== 测试增强信号处理器 ===")
    
    processor = EnhancedSignalProcessor()
    
    # 模拟趋势价格序列
    for i in range(25):
        price = 100 + i * 0.3  # 上升趋势
        market_data = {"price": price}
        result = processor.process(market_data)
        
        if i >= 20:
            assert isinstance(result, SignalOutput), "应返回SignalOutput对象"
            assert -1.0 <= result.signal <= 1.0, "信号值超出范围"
            print(f"  步骤{i+1}: 价格={price:.2f}, 信号={result.signal:.3f}")
    
    print("✓ 增强信号处理器测试通过")

def test_signal_fusion():
    """测试信号融合中心"""
    print("\n=== 测试信号融合中心 ===")
    
    fusion_center = SignalFusionCenter()
    
    # 注册处理器
    fusion_center.register_processor(UnifiedFuzzyProcessor(), weight=1.0)
    fusion_center.register_processor(StructuralAnalysisProcessor(), weight=0.8)
    fusion_center.register_processor(StatisticalAnalysisProcessor(), weight=0.8)
    
    # 测试不同融合策略
    market_data = {"price": 100.5, "trend": 0.2, "volatility": 0.03}
    
    for strategy in ['weighted_average', 'voting']:
        fusion_center.current_strategy = strategy
        result = fusion_center.process_signals(market_data)
        assert isinstance(result, SignalOutput), f"融合策略{strategy}: 应返回SignalOutput对象"
        print(f"  融合策略{strategy}: 信号={result.signal:.3f}, 置信度={result.confidence:.3f}")
    
    print("✓ 信号融合中心测试通过")

def test_strategy_integration():
    """测试策略整体集成"""
    print("\n=== 测试策略整体集成 ===")
    
    strategy = Strategy3Optimized()
    
    # 模拟K线数据序列
    prices = [100, 100.5, 101, 100.8, 101.2, 101.5, 101.3, 102, 101.8, 102.5]
    
    for i, price in enumerate(prices):
        kline = TestKLineData(price)
        
        # 测试callback方法
        strategy.callback(kline)
        
        # 检查状态更新
        assert hasattr(strategy.state_map, 'final_signal'), "应有最终信号状态"
        assert hasattr(strategy.state_map, 'signal_confidence'), "应有信号置信度状态"
        
        print(f"  K线{i+1}: 价格={price}, 信号={strategy.state_map.final_signal:.3f}, "
              f"置信度={strategy.state_map.signal_confidence:.3f}")
    
    print("✓ 策略整体集成测试通过")

def test_performance_stats():
    """测试性能统计功能"""
    print("\n=== 测试性能统计功能 ===")
    
    strategy = Strategy3Optimized()
    
    # 模拟一些交易
    strategy.state_map.total_trades = 10
    strategy.state_map.winning_trades = 7
    strategy.state_map.total_profit = 150.0
    
    strategy._update_performance_stats()
    
    assert strategy.state_map.win_rate == 0.7, "胜率计算错误"
    print(f"  总交易: {strategy.state_map.total_trades}")
    print(f"  胜率: {strategy.state_map.win_rate:.1%}")
    print(f"  总盈利: {strategy.state_map.total_profit}")
    
    print("✓ 性能统计功能测试通过")

def main():
    """主测试函数"""
    print("开始测试 Strategy3 优化版本...")
    
    try:
        test_signal_output()
        test_fuzzy_processor()
        test_structural_analysis()
        test_statistical_analysis()
        test_enhanced_processor()
        test_signal_fusion()
        test_strategy_integration()
        test_performance_stats()
        
        print("\n" + "="*50)
        print("🎉 所有测试通过！Strategy3 优化版本功能正常")
        print("="*50)
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
