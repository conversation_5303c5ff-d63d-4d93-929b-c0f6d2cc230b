# -*- coding: utf-8 -*-
"""
Strategy3_optimized.py - 高级模糊推理交易策略 (优化版本)
基于无限易Pro交易软件架构的综合交易策略

优化特性：
1. 统一信号输出格式：{"signal": float, "confidence": float, "details": dict}
2. 模块化信号处理器架构
3. 集成的信号融合中心
4. 简化的模糊推理系统
5. 合并的数学分析模块
6. 保留所有核心功能，提升可维护性
"""

from typing import Literal, Optional, List, Tuple, Dict, Any, Callable
import numpy as np
import math
import time
from collections import deque
import warnings
warnings.filterwarnings('ignore')
from pythongo.base import BaseParams, BaseState, Field
from pythongo.classdef import KLineData, OrderData, TickData, TradeData
from pythongo.ui import BaseStrategy
from pythongo.utils import KLineGenerator



# ==================== 统一信号接口 ====================

class SignalOutput:
    """统一信号输出格式"""
    def __init__(self, signal: float = 0.0, confidence: float = 0.0, details: Dict[str, Any] = None):
        self.signal = max(-1.0, min(1.0, signal))  # 限制在[-1,1]
        self.confidence = max(0.0, min(1.0, confidence))  # 限制在[0,1]
        self.details = details or {}
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            "signal": self.signal,
            "confidence": self.confidence,
            "details": self.details
        }

class BaseSignalProcessor:
    """信号处理器基类"""
    def __init__(self, name: str):
        self.name = name
        self.enabled = True
        
    def process(self, market_data: Dict[str, float]) -> SignalOutput:
        """处理市场数据并返回统一格式信号"""
        if not self.enabled:
            return SignalOutput()
        return self._process_internal(market_data)
    
    def _process_internal(self, market_data: Dict[str, float]) -> SignalOutput:
        """子类实现的具体处理逻辑"""
        raise NotImplementedError

# ==================== 策略参数配置 ====================

class Params:
    """策略参数类"""
    def __init__(self):
        # 技术指标参数
        self.hull_period = 9
        self.stc_period = 10
        self.stc_fast = 23
        self.stc_slow = 50

        # 信号阈值
        self.signal_threshold = 0.3
        self.confidence_threshold = 0.5

        # 风险管理
        self.stop_loss = 0.02
        self.take_profit = 0.04
        self.max_position = 5

        # 交易参数
        self.order_volume = 1
        self.price_type = "D1"
        self.trade_direction = "buy"

        # 模块开关
        self.enable_fuzzy = True
        self.enable_enhanced = True
        self.enable_structural = True
        self.enable_statistical = True

class State:
    """策略状态类"""
    def __init__(self):
        # 技术指标状态
        self.hull_value = 0.0
        self.stc_value = 0.0

        # 信号状态
        self.primary_signal = 0.0
        self.fuzzy_signal = 0.0
        self.final_signal = 0.0
        self.signal_confidence = 0.0

        # 市场状态
        self.trend_strength = 0.0
        self.volatility = 0.03
        self.volume_ratio = 1.0

        # 交易状态
        self.position = 0
        self.last_trade_time = 0.0
        self.long_signal = False
        self.short_signal = False

        # 性能统计
        self.total_trades = 0
        self.winning_trades = 0
        self.total_profit = 0.0
        self.win_rate = 0.0
        self.sharpe_ratio = 0.0
        self.max_drawdown = 0.0

# ==================== 信号融合中心 ====================

class SignalFusionCenter:
    """统一的信号融合与调度中心"""
    def __init__(self):
        self.processors = {}  # 注册的信号处理器
        self.weights = {}     # 处理器权重
        self.performance_history = {}  # 性能历史
        self.fusion_strategies = ['weighted_average', 'bayesian', 'voting']
        self.current_strategy = 'weighted_average'
        
    def register_processor(self, processor: BaseSignalProcessor, weight: float = 1.0):
        """注册信号处理器"""
        self.processors[processor.name] = processor
        self.weights[processor.name] = weight
        self.performance_history[processor.name] = deque(maxlen=50)
        
    def process_signals(self, market_data: Dict[str, float]) -> SignalOutput:
        """处理所有信号并融合"""
        # 收集各处理器的信号
        signals = {}
        for name, processor in self.processors.items():
            try:
                signal_output = processor.process(market_data)
                signals[name] = signal_output
            except Exception:
                signals[name] = SignalOutput()  # 默认信号
                
        # 根据策略融合信号
        if self.current_strategy == 'weighted_average':
            return self._weighted_average_fusion(signals)
        elif self.current_strategy == 'bayesian':
            return self._bayesian_fusion(signals)
        else:
            return self._voting_fusion(signals)
    
    def _weighted_average_fusion(self, signals: Dict[str, SignalOutput]) -> SignalOutput:
        """加权平均融合"""
        total_weight = 0.0
        weighted_signal = 0.0
        weighted_confidence = 0.0
        
        for name, signal_output in signals.items():
            weight = self.weights.get(name, 1.0) * signal_output.confidence
            weighted_signal += signal_output.signal * weight
            weighted_confidence += signal_output.confidence * weight
            total_weight += weight
            
        if total_weight > 0:
            final_signal = weighted_signal / total_weight
            final_confidence = weighted_confidence / total_weight
        else:
            final_signal = 0.0
            final_confidence = 0.0
            
        return SignalOutput(final_signal, final_confidence, {
            'fusion_strategy': 'weighted_average',
            'component_signals': {name: s.to_dict() for name, s in signals.items()}
        })
    
    def _bayesian_fusion(self, signals: Dict[str, SignalOutput]) -> SignalOutput:
        """贝叶斯融合（简化版）"""
        # 简化的贝叶斯融合实现
        return self._weighted_average_fusion(signals)
    
    def _voting_fusion(self, signals: Dict[str, SignalOutput]) -> SignalOutput:
        """投票融合"""
        votes = {'buy': 0, 'sell': 0, 'hold': 0}
        confidences = []
        
        for signal_output in signals.values():
            if signal_output.signal > 0.1:
                votes['buy'] += 1
            elif signal_output.signal < -0.1:
                votes['sell'] += 1
            else:
                votes['hold'] += 1
            confidences.append(signal_output.confidence)
        
        # 确定多数决策
        majority_decision = max(votes.keys(), key=lambda k: votes[k])
        
        if majority_decision == 'buy':
            final_signal = 0.5
        elif majority_decision == 'sell':
            final_signal = -0.5
        else:
            final_signal = 0.0
            
        final_confidence = np.mean(confidences) if confidences else 0.0
        
        return SignalOutput(final_signal, final_confidence, {
            'fusion_strategy': 'voting',
            'votes': votes,
            'majority_decision': majority_decision
        })

# ==================== 技术指标实现 ====================

class HullMovingAverage:
    """Hull移动平均线"""
    def __init__(self, period: int = 9):
        self.period = period
        self.wma_half = deque(maxlen=period//2)
        self.wma_full = deque(maxlen=period)
        self.hull_values = deque(maxlen=period//2)

    def update(self, price: float) -> float:
        """更新Hull MA值"""
        self.wma_full.append(price)
        self.wma_half.append(price)

        if len(self.wma_full) >= self.period:
            # 计算WMA(period/2)
            wma_half_val = self._calculate_wma(list(self.wma_half), len(self.wma_half))

            # 计算WMA(period)
            wma_full_val = self._calculate_wma(list(self.wma_full), self.period)

            # Hull计算：2*WMA(period/2) - WMA(period)
            hull_raw = 2 * wma_half_val - wma_full_val
            self.hull_values.append(hull_raw)

            # 对Hull原始值再做WMA平滑
            if len(self.hull_values) >= len(self.hull_values):
                return self._calculate_wma(list(self.hull_values), len(self.hull_values))

        return price

    def _calculate_wma(self, values: List[float], period: int) -> float:
        """计算加权移动平均"""
        if len(values) < period:
            return np.mean(values) if values else 0.0

        weights = np.arange(1, period + 1)
        weighted_sum = np.sum(weights * np.array(values[-period:]))
        weight_sum = np.sum(weights)

        return weighted_sum / weight_sum if weight_sum > 0 else 0.0

class SchaffTrendCycle:
    """Schaff趋势周期指标"""
    def __init__(self, period: int = 10, fast: int = 23, slow: int = 50):
        self.period = period
        self.fast = fast
        self.slow = slow
        self.prices = deque(maxlen=max(fast, slow) + period)
        self.macd_values = deque(maxlen=period)
        self.stoch_values = deque(maxlen=period)

    def update(self, price: float) -> float:
        """更新STC值"""
        self.prices.append(price)

        if len(self.prices) >= max(self.fast, self.slow):
            # 计算MACD
            ema_fast = self._calculate_ema(list(self.prices), self.fast)
            ema_slow = self._calculate_ema(list(self.prices), self.slow)
            macd = ema_fast - ema_slow
            self.macd_values.append(macd)

            if len(self.macd_values) >= self.period:
                # 第一次随机指标计算
                stoch1 = self._calculate_stochastic(list(self.macd_values))
                self.stoch_values.append(stoch1)

                if len(self.stoch_values) >= self.period:
                    # 第二次随机指标计算
                    stoch2 = self._calculate_stochastic(list(self.stoch_values))
                    return stoch2

        return 50.0  # 默认中性值

    def _calculate_ema(self, values: List[float], period: int) -> float:
        """计算指数移动平均"""
        if len(values) < period:
            return np.mean(values) if values else 0.0

        alpha = 2.0 / (period + 1)
        ema = values[0]

        for value in values[1:]:
            ema = alpha * value + (1 - alpha) * ema

        return ema

    def _calculate_stochastic(self, values: List[float]) -> float:
        """计算随机指标"""
        if len(values) < self.period:
            return 50.0

        recent_values = values[-self.period:]
        highest = max(recent_values)
        lowest = min(recent_values)
        current = recent_values[-1]

        if highest == lowest:
            return 50.0

        stoch = 100 * (current - lowest) / (highest - lowest)
        return max(0.0, min(100.0, stoch))

# ==================== 统一模糊推理系统 ====================

class UnifiedFuzzyProcessor(BaseSignalProcessor):
    """统一模糊推理处理器"""
    def __init__(self):
        super().__init__("fuzzy_processor")
        self.fuzzy_sets = {}
        self.rules = []
        self._initialize_fuzzy_sets()
        self._initialize_rules()

    def _process_internal(self, market_data: Dict[str, float]) -> SignalOutput:
        """处理模糊推理信号"""
        try:
            # 模糊化
            fuzzified = self._fuzzify(market_data)

            # 规则推理
            rule_outputs = self._apply_rules(fuzzified)

            # 去模糊化
            crisp_output = self._defuzzify(rule_outputs)

            # 计算置信度
            confidence = self._calculate_confidence(fuzzified, rule_outputs)

            return SignalOutput(crisp_output, confidence, {
                'fuzzified': fuzzified,
                'rule_outputs': rule_outputs,
                'defuzzified': crisp_output
            })

        except Exception:
            return SignalOutput()

    def _initialize_fuzzy_sets(self):
        """初始化模糊集"""
        # 趋势强度模糊集
        self.fuzzy_sets['trend_weak'] = lambda x: max(0, 1 - abs(x) * 5)
        self.fuzzy_sets['trend_strong_up'] = lambda x: max(0, min(1, x * 5)) if x > 0 else 0
        self.fuzzy_sets['trend_strong_down'] = lambda x: max(0, min(1, -x * 5)) if x < 0 else 0

        # 波动率模糊集
        self.fuzzy_sets['volatility_low'] = lambda x: max(0, 1 - x * 20)
        self.fuzzy_sets['volatility_medium'] = lambda x: max(0, 1 - abs(x - 0.03) * 20)
        self.fuzzy_sets['volatility_high'] = lambda x: max(0, min(1, (x - 0.05) * 20))

        # 输出模糊集
        self.fuzzy_sets['strong_buy'] = lambda x: max(0, min(1, x * 2)) if x > 0.5 else 0
        self.fuzzy_sets['weak_buy'] = lambda x: max(0, min(1, x * 4)) if 0 < x <= 0.5 else 0
        self.fuzzy_sets['hold'] = lambda x: max(0, 1 - abs(x) * 4)
        self.fuzzy_sets['weak_sell'] = lambda x: max(0, min(1, -x * 4)) if -0.5 <= x < 0 else 0
        self.fuzzy_sets['strong_sell'] = lambda x: max(0, min(1, -x * 2)) if x < -0.5 else 0

    def _initialize_rules(self):
        """初始化模糊规则"""
        self.rules = [
            # 强趋势规则
            {'if': ['trend_strong_up', 'volatility_low'], 'then': 'strong_buy', 'weight': 1.0},
            {'if': ['trend_strong_up', 'volatility_medium'], 'then': 'weak_buy', 'weight': 0.8},
            {'if': ['trend_strong_down', 'volatility_low'], 'then': 'strong_sell', 'weight': 1.0},
            {'if': ['trend_strong_down', 'volatility_medium'], 'then': 'weak_sell', 'weight': 0.8},

            # 弱趋势规则
            {'if': ['trend_weak', 'volatility_low'], 'then': 'hold', 'weight': 0.9},
            {'if': ['trend_weak', 'volatility_high'], 'then': 'hold', 'weight': 0.7},

            # 高波动率规则
            {'if': ['volatility_high'], 'then': 'hold', 'weight': 0.6},
        ]

    def _fuzzify(self, market_data: Dict[str, float]) -> Dict[str, float]:
        """模糊化输入"""
        fuzzified = {}

        trend = market_data.get('trend', 0.0)
        volatility = market_data.get('volatility', 0.03)

        # 计算各模糊集的隶属度
        fuzzified['trend_weak'] = self.fuzzy_sets['trend_weak'](trend)
        fuzzified['trend_strong_up'] = self.fuzzy_sets['trend_strong_up'](trend)
        fuzzified['trend_strong_down'] = self.fuzzy_sets['trend_strong_down'](trend)

        fuzzified['volatility_low'] = self.fuzzy_sets['volatility_low'](volatility)
        fuzzified['volatility_medium'] = self.fuzzy_sets['volatility_medium'](volatility)
        fuzzified['volatility_high'] = self.fuzzy_sets['volatility_high'](volatility)

        return fuzzified

    def _apply_rules(self, fuzzified: Dict[str, float]) -> Dict[str, float]:
        """应用模糊规则"""
        rule_outputs = {}

        for rule in self.rules:
            # 计算规则激活度
            activation = 1.0
            for condition in rule['if']:
                activation = min(activation, fuzzified.get(condition, 0.0))

            # 应用权重
            activation *= rule['weight']

            # 累积输出
            output_set = rule['then']
            if output_set not in rule_outputs:
                rule_outputs[output_set] = 0.0
            rule_outputs[output_set] = max(rule_outputs[output_set], activation)

        return rule_outputs

    def _defuzzify(self, rule_outputs: Dict[str, float]) -> float:
        """去模糊化（重心法）"""
        try:
            # 定义输出值
            output_values = {
                'strong_buy': 0.8,
                'weak_buy': 0.3,
                'hold': 0.0,
                'weak_sell': -0.3,
                'strong_sell': -0.8
            }

            numerator = 0.0
            denominator = 0.0

            for output_set, activation in rule_outputs.items():
                if output_set in output_values and activation > 0:
                    value = output_values[output_set]
                    numerator += value * activation
                    denominator += activation

            if denominator > 0:
                return numerator / denominator
            else:
                return 0.0

        except Exception:
            return 0.0

    def _calculate_confidence(self, fuzzified: Dict[str, float], rule_outputs: Dict[str, float]) -> float:
        """计算置信度"""
        try:
            # 基于规则激活度计算置信度
            max_activation = max(rule_outputs.values()) if rule_outputs else 0.0
            avg_activation = np.mean(list(rule_outputs.values())) if rule_outputs else 0.0

            # 基于输入模糊度计算置信度
            input_clarity = max(fuzzified.values()) if fuzzified else 0.0

            # 综合置信度
            confidence = (max_activation * 0.5 + avg_activation * 0.3 + input_clarity * 0.2)
            return min(1.0, max(0.0, confidence))

        except Exception:
            return 0.0

# ==================== 合并的数学分析模块 ====================

class StructuralAnalysisProcessor(BaseSignalProcessor):
    """结构性特征分析模块（合并群论+拓扑+对称性）"""
    def __init__(self):
        super().__init__("structural_analysis")
        self.price_buffer = deque(maxlen=50)
        self.symmetry_history = deque(maxlen=20)

    def _process_internal(self, market_data: Dict[str, float]) -> SignalOutput:
        """处理结构性特征"""
        try:
            price = market_data.get('price', 100.0)
            self.price_buffer.append(price)

            if len(self.price_buffer) < 10:
                return SignalOutput()

            # 对称性分析
            symmetry_strength = self._analyze_symmetry()

            # 拓扑连续性分析
            continuity_score = self._analyze_continuity()

            # 群不变性分析
            invariance_score = self._analyze_invariance()

            # 综合结构信号
            structural_signal = (symmetry_strength * 0.4 +
                               continuity_score * 0.3 +
                               invariance_score * 0.3)

            confidence = min(0.9, (symmetry_strength + continuity_score + invariance_score) / 3.0)

            return SignalOutput(structural_signal, confidence, {
                'symmetry_strength': symmetry_strength,
                'continuity_score': continuity_score,
                'invariance_score': invariance_score
            })

        except Exception:
            return SignalOutput()

    def _analyze_symmetry(self) -> float:
        """分析价格序列的对称性"""
        try:
            prices = np.array(list(self.price_buffer))
            normalized = (prices - np.mean(prices)) / (np.std(prices) + 1e-8)

            # 反射对称性
            reflected = normalized[::-1]
            reflection_corr = np.corrcoef(normalized, reflected)[0, 1]

            # 平移对称性（自相关）
            if len(normalized) > 5:
                autocorr = np.correlate(normalized, normalized, mode='full')
                autocorr = autocorr[autocorr.size // 2:]
                autocorr = autocorr / autocorr[0]
                translation_symmetry = np.max(autocorr[1:5]) if len(autocorr) > 5 else 0.0
            else:
                translation_symmetry = 0.0

            symmetry = (abs(reflection_corr) * 0.6 + translation_symmetry * 0.4)
            return float(max(0.0, min(1.0, symmetry)))

        except Exception:
            return 0.0

    def _analyze_continuity(self) -> float:
        """分析价格序列的拓扑连续性"""
        try:
            prices = np.array(list(self.price_buffer))
            if len(prices) < 3:
                return 0.0

            # 计算价格变化的连续性
            changes = np.diff(prices)
            change_variance = np.var(changes)

            # 连续性评分（方差越小越连续）
            continuity = 1.0 / (1.0 + change_variance * 100)
            return float(max(0.0, min(1.0, continuity)))

        except Exception:
            return 0.0

    def _analyze_invariance(self) -> float:
        """分析群不变性"""
        try:
            prices = np.array(list(self.price_buffer))
            if len(prices) < 5:
                return 0.0

            # 简化的不变性度量：价格序列的稳定性
            normalized = (prices - np.mean(prices)) / (np.std(prices) + 1e-8)

            # 计算序列的"不变性"（稳定性）
            stability = 1.0 / (1.0 + np.var(normalized))
            return float(max(0.0, min(1.0, stability)))

        except Exception:
            return 0.0

class StatisticalAnalysisProcessor(BaseSignalProcessor):
    """统计特征分析模块（合并概率+泛函分析+信息论）"""
    def __init__(self):
        super().__init__("statistical_analysis")
        self.price_buffer = deque(maxlen=100)
        self.entropy_window = 20

    def _process_internal(self, market_data: Dict[str, float]) -> SignalOutput:
        """处理统计特征"""
        try:
            price = market_data.get('price', 100.0)
            volatility = market_data.get('volatility', 0.02)

            self.price_buffer.append(price)

            if len(self.price_buffer) < 10:
                return SignalOutput()

            # 概率分析
            probability_signal = self._analyze_probability(volatility)

            # 信息熵分析
            entropy_signal = self._analyze_entropy()

            # 泛函分析（简化为频域分析）
            functional_signal = self._analyze_functional()

            # 综合统计信号
            statistical_signal = (probability_signal * 0.4 +
                                 entropy_signal * 0.3 +
                                 functional_signal * 0.3)

            confidence = min(0.9, abs(statistical_signal) + 0.3)

            return SignalOutput(statistical_signal, confidence, {
                'probability_signal': probability_signal,
                'entropy_signal': entropy_signal,
                'functional_signal': functional_signal
            })

        except Exception:
            return SignalOutput()

    def _analyze_probability(self, volatility: float) -> float:
        """概率分析"""
        try:
            # 基于波动率的概率信号
            if volatility > 0.05:
                return -0.3  # 高波动，保守信号
            elif volatility < 0.02:
                return 0.2   # 低波动，积极信号
            else:
                return 0.0   # 中等波动，中性信号
        except Exception:
            return 0.0

    def _analyze_entropy(self) -> float:
        """信息熵分析"""
        try:
            if len(self.price_buffer) < self.entropy_window:
                return 0.0

            prices = np.array(list(self.price_buffer)[-self.entropy_window:])

            # 计算价格变化的熵
            changes = np.diff(prices)
            if len(changes) == 0:
                return 0.0

            # 离散化价格变化
            hist, _ = np.histogram(changes, bins=5)
            probabilities = hist / np.sum(hist)
            probabilities = probabilities[probabilities > 0]

            # 计算Shannon熵
            if len(probabilities) > 0:
                entropy = -np.sum(probabilities * np.log2(probabilities))
                # 熵越高，不确定性越大，信号越保守
                entropy_signal = -entropy / 3.0  # 归一化
                return float(max(-1.0, min(1.0, entropy_signal)))
            else:
                return 0.0

        except Exception:
            return 0.0

    def _analyze_functional(self) -> float:
        """泛函分析（频域特征）"""
        try:
            if len(self.price_buffer) < 16:
                return 0.0

            prices = np.array(list(self.price_buffer)[-16:])

            # FFT分析
            fft_result = np.fft.fft(prices)
            power_spectrum = np.abs(fft_result) ** 2

            # 分析主导频率
            dominant_freq_idx = np.argmax(power_spectrum[1:len(power_spectrum)//2]) + 1

            # 基于主导频率生成信号
            if dominant_freq_idx < 3:
                return 0.1  # 低频主导，趋势信号
            elif dominant_freq_idx > 6:
                return -0.1  # 高频主导，震荡信号
            else:
                return 0.0  # 中频，中性信号

        except Exception:
            return 0.0

class EnhancedSignalProcessor(BaseSignalProcessor):
    """增强信号处理器（合并机器学习+数学预测+模式识别）"""
    def __init__(self):
        super().__init__("enhanced_signal")
        self.price_buffer = deque(maxlen=200)
        self.feature_buffer = deque(maxlen=50)

    def _process_internal(self, market_data: Dict[str, float]) -> SignalOutput:
        """处理增强信号"""
        try:
            price = market_data.get('price', 100.0)

            self.price_buffer.append(price)

            if len(self.price_buffer) < 20:
                return SignalOutput()

            # 特征提取
            features = self._extract_features()

            # 趋势检测
            trend_signal = self._detect_trend()

            # 模式匹配
            pattern_signal = self._match_patterns()

            # 数学预测
            prediction_signal = self._mathematical_prediction()

            # 综合增强信号
            enhanced_signal = (trend_signal * 0.3 +
                             pattern_signal * 0.3 +
                             prediction_signal * 0.4)

            confidence = min(0.9, abs(enhanced_signal) + 0.2)

            return SignalOutput(enhanced_signal, confidence, {
                'trend_signal': trend_signal,
                'pattern_signal': pattern_signal,
                'prediction_signal': prediction_signal,
                'features': features
            })

        except Exception:
            return SignalOutput()

    def _extract_features(self) -> Dict[str, float]:
        """提取市场特征"""
        try:
            prices = np.array(list(self.price_buffer)[-20:])

            # 基础统计特征
            mean_price = np.mean(prices)
            std_price = np.std(prices)

            # 技术指标特征
            rsi = self._calculate_rsi(prices)
            momentum = (prices[-1] - prices[-10]) / prices[-10] if len(prices) >= 10 else 0.0

            # 波动率特征
            returns = np.diff(prices) / prices[:-1]
            volatility = np.std(returns) if len(returns) > 0 else 0.0

            features = {
                'mean_price': mean_price,
                'std_price': std_price,
                'rsi': rsi,
                'momentum': momentum,
                'volatility': volatility
            }

            self.feature_buffer.append(features)
            return features

        except Exception:
            return {}

    def _calculate_rsi(self, prices: np.ndarray, period: int = 14) -> float:
        """计算RSI指标"""
        try:
            if len(prices) < period + 1:
                return 50.0

            deltas = np.diff(prices)
            gains = np.where(deltas > 0, deltas, 0)
            losses = np.where(deltas < 0, -deltas, 0)

            avg_gain = np.mean(gains[-period:])
            avg_loss = np.mean(losses[-period:])

            if avg_loss == 0:
                return 100.0

            rs = avg_gain / avg_loss
            rsi = 100 - (100 / (1 + rs))

            return float(rsi)

        except Exception:
            return 50.0

    def _detect_trend(self) -> float:
        """检测趋势"""
        try:
            if len(self.price_buffer) < 10:
                return 0.0

            prices = np.array(list(self.price_buffer)[-10:])

            # 线性回归趋势
            x = np.arange(len(prices))
            slope, _ = np.polyfit(x, prices, 1)

            # 归一化斜率
            normalized_slope = slope / np.mean(prices)

            # 限制在[-1, 1]范围
            trend_signal = max(-1.0, min(1.0, normalized_slope * 100))

            return float(trend_signal)

        except Exception:
            return 0.0

    def _match_patterns(self) -> float:
        """模式匹配"""
        try:
            if len(self.price_buffer) < 20:
                return 0.0

            current_pattern = np.array(list(self.price_buffer)[-10:])
            current_pattern = (current_pattern - np.mean(current_pattern)) / (np.std(current_pattern) + 1e-8)

            # 与历史模式比较
            best_match_score = 0.0
            best_match_signal = 0.0

            for i in range(len(self.price_buffer) - 20):
                historical_pattern = np.array(list(self.price_buffer)[i:i+10])
                historical_pattern = (historical_pattern - np.mean(historical_pattern)) / (np.std(historical_pattern) + 1e-8)

                # 计算相关性
                correlation = np.corrcoef(current_pattern, historical_pattern)[0, 1]

                if abs(correlation) > best_match_score:
                    best_match_score = abs(correlation)
                    # 基于历史模式后续走势预测信号
                    if i + 15 < len(self.price_buffer):
                        future_change = (list(self.price_buffer)[i+15] - list(self.price_buffer)[i+10]) / list(self.price_buffer)[i+10]
                        best_match_signal = max(-1.0, min(1.0, future_change * 10))

            return float(best_match_signal * best_match_score)

        except Exception:
            return 0.0

    def _mathematical_prediction(self) -> float:
        """数学预测"""
        try:
            if len(self.price_buffer) < 15:
                return 0.0

            prices = np.array(list(self.price_buffer)[-15:])

            # 多项式拟合预测
            x = np.arange(len(prices))
            coeffs = np.polyfit(x, prices, 2)  # 二次多项式

            # 预测下一个点
            next_x = len(prices)
            predicted_price = np.polyval(coeffs, next_x)

            # 计算预测信号
            current_price = prices[-1]
            prediction_change = (predicted_price - current_price) / current_price

            prediction_signal = max(-1.0, min(1.0, prediction_change * 20))

            return float(prediction_signal)

        except Exception:
            return 0.0

# ==================== 主策略类 ====================

class Strategy31(BaseStrategy):
    """高级模糊推理交易策略 - 优化版本"""

    def __init__(self):
        super().__init__()
        self.params_map = Params()
        self.state_map = State()

        # 技术指标
        self.hull_ma = HullMovingAverage(self.params_map.hull_period)
        self.stc = SchaffTrendCycle(
            self.params_map.stc_period,
            self.params_map.stc_fast,
            self.params_map.stc_slow
        )

        # 信号融合中心
        self.signal_fusion = SignalFusionCenter()

        # 初始化信号处理器
        self._initialize_processors()

        # 交易状态
        self.long_signal = False
        self.short_signal = False
        self.last_signal_time = 0.0

        print("Strategy3Optimized 高级模糊推理策略初始化完成")

    def _initialize_processors(self):
        """初始化信号处理器"""
        try:
            # 注册各种信号处理器
            if self.params_map.enable_fuzzy:
                fuzzy_processor = UnifiedFuzzyProcessor()
                self.signal_fusion.register_processor(fuzzy_processor, weight=1.0)

            if self.params_map.enable_structural:
                structural_processor = StructuralAnalysisProcessor()
                self.signal_fusion.register_processor(structural_processor, weight=0.8)

            if self.params_map.enable_statistical:
                statistical_processor = StatisticalAnalysisProcessor()
                self.signal_fusion.register_processor(statistical_processor, weight=0.8)

            if self.params_map.enable_enhanced:
                enhanced_processor = EnhancedSignalProcessor()
                self.signal_fusion.register_processor(enhanced_processor, weight=1.2)

        except Exception as e:
            print(f"信号处理器初始化错误: {e}")

    def on_tick(self, tick):
        """tick数据处理"""
        try:
            # 更新市场数据
            self._update_market_data(tick)

            # 生成交易信号
            signal_strength = self.calc_signal(tick)

            # 执行交易逻辑
            self._execute_trading_logic(signal_strength, tick)

        except Exception as e:
            print(f"on_tick处理错误: {e}")

    def _update_market_data(self, tick):
        """更新市场数据"""
        try:
            # 更新技术指标
            self.state_map.hull_value = self.hull_ma.update(tick.close)
            self.state_map.stc_value = self.stc.update(tick.close)

            # 计算趋势强度
            if hasattr(self.hull_ma, 'hull_values') and len(self.hull_ma.hull_values) >= 2:
                hull_change = (self.hull_ma.hull_values[-1] - self.hull_ma.hull_values[-2]) / self.hull_ma.hull_values[-2]
                self.state_map.trend_strength = max(-1.0, min(1.0, hull_change * 100))

            # 计算波动率
            if len(self.hull_ma.wma_full) >= 10:
                prices = np.array(list(self.hull_ma.wma_full)[-10:])
                returns = np.diff(prices) / prices[:-1]
                self.state_map.volatility = np.std(returns) if len(returns) > 0 else 0.03

        except Exception as e:
            print(f"市场数据更新错误: {e}")

    def calc_signal(self, kline) -> float:
        """计算综合交易信号"""
        try:
            # 准备市场数据
            market_data = {
                'price': kline.close,
                'trend': self.state_map.trend_strength,
                'volatility': self.state_map.volatility,
                'volume': getattr(kline, 'volume', 1000.0),
                'hull_value': self.state_map.hull_value,
                'stc_value': self.state_map.stc_value
            }

            # 通过信号融合中心处理
            fusion_result = self.signal_fusion.process_signals(market_data)

            # 更新状态
            self.state_map.final_signal = fusion_result.signal
            self.state_map.signal_confidence = fusion_result.confidence

            return fusion_result.signal

        except Exception as e:
            print(f"信号计算错误: {e}")
            return 0.0

    def _execute_trading_logic(self, signal_strength: float, kline):
        """执行交易逻辑"""
        try:
            current_time = time.time()

            # 检查信号强度和置信度
            if (abs(signal_strength) < self.params_map.signal_threshold or
                self.state_map.signal_confidence < self.params_map.confidence_threshold):
                self.long_signal = False
                self.short_signal = False
                return

            # 防止频繁交易
            if current_time - self.last_signal_time < 60:  # 60秒间隔
                return

            # 生成交易信号
            if signal_strength > self.params_map.signal_threshold:
                self.long_signal = True
                self.short_signal = False
                self._execute_buy_signal(kline)
            elif signal_strength < -self.params_map.signal_threshold:
                self.long_signal = False
                self.short_signal = True
                self._execute_sell_signal(kline)
            else:
                self.long_signal = False
                self.short_signal = False

            self.last_signal_time = current_time

        except Exception as e:
            print(f"交易逻辑执行错误: {e}")

    def _execute_buy_signal(self, kline):
        """执行买入信号"""
        try:
            if self.state_map.position < self.params_map.max_position:
                self.buy(self.params_map.order_volume, self.params_map.price_type)
                self.state_map.position += self.params_map.order_volume
                self.state_map.total_trades += 1
                print(f"执行买入: 价格={kline.close}, 信号强度={self.state_map.final_signal:.3f}")

        except Exception as e:
            print(f"买入执行错误: {e}")

    def _execute_sell_signal(self, kline):
        """执行卖出信号"""
        try:
            if self.state_map.position > -self.params_map.max_position:
                self.sell(self.params_map.order_volume, self.params_map.price_type)
                self.state_map.position -= self.params_map.order_volume
                self.state_map.total_trades += 1
                print(f"执行卖出: 价格={kline.close}, 信号强度={self.state_map.final_signal:.3f}")

        except Exception as e:
            print(f"卖出执行错误: {e}")

    def callback(self, kline: KLineData):
        """K线回调函数"""
        try:
            # 更新市场数据
            self._update_market_data(kline)

            # 计算信号
            signal_strength = self.calc_signal(kline)

            # 执行交易逻辑
            self._execute_trading_logic(signal_strength, kline)

            # 更新性能统计
            self._update_performance_stats()

        except Exception as e:
            print(f"callback处理错误: {e}")

    def _update_performance_stats(self):
        """更新性能统计"""
        try:
            if self.state_map.total_trades > 0:
                self.state_map.win_rate = self.state_map.winning_trades / self.state_map.total_trades
            else:
                self.state_map.win_rate = 0.0

        except Exception as e:
            print(f"性能统计更新错误: {e}")

    def on_start(self):
        """策略启动"""
        print("Strategy3Optimized 策略启动")
        self.state_map.last_trade_time = time.time()

    def on_stop(self):
        """策略停止"""
        print("Strategy3Optimized 策略停止")
        print(f"总交易次数: {self.state_map.total_trades}")
        print(f"胜率: {self.state_map.win_rate:.2%}")
        print(f"总盈利: {self.state_map.total_profit:.2f}")

# ==================== 策略实例化 ====================

# 策略实例在需要时创建
# strategy = Strategy3Optimized()

# ==================== 策略说明 ====================
"""
Strategy3Optimized 高级模糊推理交易策略 - 优化版本

优化特点：
1. 统一信号接口：所有模块输出标准化的SignalOutput格式
2. 模块化架构：各功能模块独立，便于维护和扩展
3. 信号融合中心：支持多种融合策略，可配置切换
4. 简化的模糊推理：保留核心功能，提升性能
5. 合并的数学模块：减少冗余，统一接口

核心模块：
- UnifiedFuzzyProcessor: 统一模糊推理处理器
- StructuralAnalysisProcessor: 结构性特征分析（群论+拓扑+对称性）
- StatisticalAnalysisProcessor: 统计特征分析（概率+泛函+信息论）
- EnhancedSignalProcessor: 增强信号处理（机器学习+预测+模式识别）
- SignalFusionCenter: 信号融合与调度中心

技术指标：
- Hull移动平均线：快速响应价格变化
- STC指标：Schaff趋势周期，识别趋势转折点

风险管理：
- 可配置的信号阈值和置信度阈值
- 防频繁交易机制
- 持仓限制和风险控制

使用方法：
1. 调整Params类中的参数
2. 通过模块开关控制功能启用
3. 监控State类中的状态信息
4. 根据性能统计优化策略参数
"""
