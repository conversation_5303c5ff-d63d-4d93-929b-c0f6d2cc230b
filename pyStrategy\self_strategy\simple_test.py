# -*- coding: utf-8 -*-
"""
简单测试脚本 - 验证Strategy3优化版本的核心功能
"""

import sys
import os
import numpy as np

# 添加路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_core_classes():
    """测试核心类"""
    print("=== 测试核心类 ===")
    
    try:
        # 测试统一信号输出
        from Strategy3_optimized import SignalOutput
        signal = SignalOutput(0.5, 0.8, {"test": True})
        print(f"✓ SignalOutput: signal={signal.signal}, confidence={signal.confidence}")
        
        # 测试参数类
        from Strategy3_optimized import Params
        params = Params()
        print(f"✓ Params: hull_period={params.hull_period}, signal_threshold={params.signal_threshold}")
        
        # 测试状态类
        from Strategy3_optimized import State
        state = State()
        print(f"✓ State: final_signal={state.final_signal}, volatility={state.volatility}")
        
        return True
        
    except Exception as e:
        print(f"✗ 核心类测试失败: {e}")
        return False

def test_technical_indicators():
    """测试技术指标"""
    print("\n=== 测试技术指标 ===")
    
    try:
        from Strategy3_optimized import HullMovingAverage, SchaffTrendCycle
        
        # 测试Hull MA
        hull = HullMovingAverage(9)
        prices = [100, 101, 102, 101.5, 103, 102.8, 104]
        for price in prices:
            hull_value = hull.update(price)
        print(f"✓ Hull MA: 最终值={hull_value:.2f}")
        
        # 测试STC
        stc = SchaffTrendCycle(10, 23, 50)
        for price in prices * 3:  # 需要更多数据
            stc_value = stc.update(price)
        print(f"✓ STC: 最终值={stc_value:.2f}")
        
        return True
        
    except Exception as e:
        print(f"✗ 技术指标测试失败: {e}")
        return False

def test_signal_processors():
    """测试信号处理器"""
    print("\n=== 测试信号处理器 ===")
    
    try:
        from Strategy3_optimized import (
            UnifiedFuzzyProcessor,
            StructuralAnalysisProcessor,
            StatisticalAnalysisProcessor,
            EnhancedSignalProcessor
        )
        
        market_data = {"price": 100.5, "trend": 0.2, "volatility": 0.03}
        
        # 测试模糊处理器
        fuzzy = UnifiedFuzzyProcessor()
        result = fuzzy.process(market_data)
        print(f"✓ 模糊处理器: signal={result.signal:.3f}, confidence={result.confidence:.3f}")
        
        # 测试结构分析处理器
        structural = StructuralAnalysisProcessor()
        # 添加一些价格数据
        for i in range(15):
            structural.price_buffer.append(100 + i * 0.1)
        result = structural.process(market_data)
        print(f"✓ 结构分析: signal={result.signal:.3f}, confidence={result.confidence:.3f}")
        
        # 测试统计分析处理器
        statistical = StatisticalAnalysisProcessor()
        for i in range(15):
            statistical.price_buffer.append(100 + i * 0.1)
        result = statistical.process(market_data)
        print(f"✓ 统计分析: signal={result.signal:.3f}, confidence={result.confidence:.3f}")
        
        # 测试增强处理器
        enhanced = EnhancedSignalProcessor()
        for i in range(25):
            enhanced.price_buffer.append(100 + i * 0.1)
        result = enhanced.process(market_data)
        print(f"✓ 增强处理器: signal={result.signal:.3f}, confidence={result.confidence:.3f}")
        
        return True
        
    except Exception as e:
        print(f"✗ 信号处理器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_signal_fusion():
    """测试信号融合"""
    print("\n=== 测试信号融合 ===")
    
    try:
        from Strategy3_optimized import SignalFusionCenter, UnifiedFuzzyProcessor
        
        fusion = SignalFusionCenter()
        
        # 注册处理器
        processor = UnifiedFuzzyProcessor()
        fusion.register_processor(processor, weight=1.0)
        
        # 测试融合
        market_data = {"price": 100.5, "trend": 0.2, "volatility": 0.03}
        result = fusion.process_signals(market_data)
        print(f"✓ 信号融合: signal={result.signal:.3f}, confidence={result.confidence:.3f}")
        
        return True
        
    except Exception as e:
        print(f"✗ 信号融合测试失败: {e}")
        return False

def test_strategy_basic():
    """测试策略基本功能"""
    print("\n=== 测试策略基本功能 ===")
    
    try:
        from Strategy3_optimized import Strategy3Optimized
        
        # 创建策略实例
        strategy = Strategy3Optimized()
        print("✓ 策略实例创建成功")
        
        # 检查初始化
        assert hasattr(strategy, 'params_map'), "缺少参数映射"
        assert hasattr(strategy, 'state_map'), "缺少状态映射"
        assert hasattr(strategy, 'signal_fusion'), "缺少信号融合中心"
        print("✓ 策略初始化检查通过")
        
        # 测试参数访问
        print(f"✓ 参数访问: hull_period={strategy.params_map.hull_period}")
        print(f"✓ 状态访问: final_signal={strategy.state_map.final_signal}")
        
        return True
        
    except Exception as e:
        print(f"✗ 策略基本功能测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("开始测试 Strategy3 优化版本核心功能...\n")
    
    tests = [
        test_core_classes,
        test_technical_indicators,
        test_signal_processors,
        test_signal_fusion,
        test_strategy_basic
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
            else:
                print("测试失败")
        except Exception as e:
            print(f"测试异常: {e}")
    
    print(f"\n{'='*50}")
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有核心功能测试通过！")
        return True
    else:
        print("❌ 部分测试失败")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
